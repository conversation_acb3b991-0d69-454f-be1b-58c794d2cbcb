import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Modal from "react-modal";
import { IoIosPeople } from "react-icons/io";
import { BsFillCaretLeftFill } from "react-icons/bs";
import { fetchWithAuth } from "../utils/api";
import AddIcon from "@mui/icons-material/Add";
import "./BrokerLoginModal.css";

import {
  TrendingUp,
  Link,
  Play,
  Target,
  BarChart3,
  Wrench,
  Zap,
  Eye,
  Square,
  RefreshCw,
  Settings,
  BarChart,
  Calendar,
  Download,
  MessageSquare,
  Save,
  Shield,
  Key,
  RotateCcw,
  Upload,
  Columns,
  FolderOpen,
  Clock,
  CheckCircle,
  Trash2,
  XCircle,
  Filter,
  Trash,
  Plus,
  MoreHorizontal
} from "lucide-react";
import { OptionQuickTradePanel } from "../components/OptionQuickTradePanel";
import { QuickTradePanel } from "../components/QuickTradePanel";
import { ExitPosition } from "../components/ExitPosition";
import { useSelector, useDispatch } from "react-redux";
import { setPlaceOrderStart } from "../store/slices/placeOrder";
import { setConsoleMsgs } from "../store/slices/consoleMsg";
import { setBrokers } from "../store/slices/broker";
import { setPortfolios } from "../store/slices/portfolio";
import { useFetchPortfolios } from "../hooks/useFetchPortfolios";
import { IoExit } from "react-icons/io5";
import Papa from "papaparse";
import Cookies from "universal-cookie";
const cookies = new Cookies();
import {
  Stop as StopIcon,
  PlayArrow as PlayArrowIcon,
} from "@mui/icons-material";

const RightCustomizeTwo = ({ rows, handleMsg }) => {
  const dispatch = useDispatch();
  const { placeOrderStart } = useSelector(
    (state) => state.placeOrderStartReducer
  );
  useEffect(() => { }, [ placeOrderStart ]);

  const handlePlaceOrderStart = (e) => {
    const btn = e.target.className;
    if (btn === "start") {
      if (placeOrderStart) {
        handleMsg({
          msg: "Trading is already started.",
          logType: "MESSAGE",
          timestamp: ` ${new Date().toLocaleString()}`,
        });
        return;
      }

      if (rows.some((row) => row.inputDisabled)) {
        dispatch(setPlaceOrderStart({ placeOrderStart: true }));
        handleMsg({
          msg: "Trading is started.",
          logType: "MESSAGE",
          timestamp: ` ${new Date().toLocaleString()}`,
        });
      } else {
        handleMsg({
          msg: "To Start Trading, At least one broker account should be Logged In",
          logType: "WARNING",
          timestamp: ` ${new Date().toLocaleString()}`,
          color: "red",
        });
      }
    } else if (btn === "stop") {
      if (!placeOrderStart) {
        handleMsg({
          msg: "Trading is already stopped.",
          logType: "MESSAGE",
          timestamp: ` ${new Date().toLocaleString()}`,
        });
        return;
      }

      dispatch(setPlaceOrderStart({ placeOrderStart: false }));
      handleMsg({
        msg: "Trading is stopped.",
        logType: "MESSAGE",
        timestamp: ` ${new Date().toLocaleString()}`,
        color: "red",
      });
    }
  };

  return (
    <div className="right-customize-two">
      <div
        className="start"
        style={{
          background: placeOrderStart ? "#595959" : "#d8e1ff",
          transition: "background 1s cubic-bezier(0.42, 0, 0.54, 1.27)",
        }}
        onClick={handlePlaceOrderStart}
      >
        <span className="start">Start</span>
        <PlayArrowIcon sx={{ color: "green" }} />
      </div>
      <div
        className="stop"
        style={{
          background: placeOrderStart ? "#d8e1ff" : "#595959",
          transition: "background 1s cubic-bezier(0.42, 0, 0.54, 1.27)",
        }}
        onClick={handlePlaceOrderStart}
      >
        <span className="stop">Stop</span>
        <StopIcon sx={{ color: "red" }} />
      </div>
    </div>
  );
};

function RightNav() {
  const [ selectAll, setSelectAll ] = useState(false);
  const token = cookies.get("TOKEN");

  const fetchPortfolios = useFetchPortfolios();

  const handleSelectAllChange = (e) => {
    const isChecked = e.target.checked;
    setSelectAll(isChecked);
    if (isChecked) {
      setSelectedRows([ ...portfolioDetails ]);
    } else {
      setSelectedRows([]);
    }
  };

  const [ ExportModal, setExportModal ] = useState(false);
  const [ ExportModal1, setExportModal1 ] = useState(false);
  const openModal = () => {
    if (pathname.includes("Portfolio")) {
      setExportModal1(true);
      setSelectAll(false);
    }
  };
  const closeModal = () => {
    setExportModal(false);
    setExportModal1(false);
    setSelectedRows([]);
  };
  const handleCancelClick = () => {
    closeModal();
  };

  const [ selectedRows, setSelectedRows ] = useState([]);
  const [ portfolios, setPortfolio ] = useState([]);
  const [ selectallExport, setSelectAllExport ] = useState(false);
  const handleCheckboxChange = (portfolio, isChecked) => {
    if (isChecked) {
      const combinedPortfolioItem = portfolios.find(
        (item) => item.portfolio_id === portfolio.portfolio_id
      );
      setSelectedRows((prev) => [ ...prev, combinedPortfolioItem ]);
    } else {
      setSelectedRows((prev) =>
        prev.filter((item) => item.portfolio_id !== portfolio.portfolio_id)
      );
    }
  };

  const handleselectallExport = (e) => {
    const isChecked = e.target.checked;
    setSelectAllExport(isChecked);

    if (isChecked) {
      const allSelectedRows = portfolios.map((portfolio) =>
        portfolios.find((item) => item.portfolio_id === portfolio.portfolio_id)
      );
      setSelectedRows(allSelectedRows);
    } else {
      setSelectedRows([]);
    }
  };
  useEffect(() => {
    const allSelected = portfolios.every((portfolio) =>
      selectedRows.some((row) => row.portfolio_id === portfolio.portfolio_id)
    );
    setSelectAllExport(allSelected);
  }, [ selectedRows, portfolios ]);

  const handleOkClick = () => {
    portfolioData(selectedRows);

    dispatch(async (dispatch, getState) => {
      const executedPortfolios =
        getState().executedPortfolioReducer.executedPortfolios;
      let portfolios = [];

      const execPortNames = executedPortfolios.map(
        (port) => port.portfolio_name
      );
      for (let i = 0; i < selectedRows.length; i++) {
        const port = selectedRows[ i ];
        if (execPortNames.includes(port.portfolio_name)) {
          let clickedPortBrokerDetails = portfolio_timings[ port.portfolio_name ];
          let prevbrokerDetails = [];
          for (let brokerId in clickedPortBrokerDetails) {
            let brokerData = clickedPortBrokerDetails[ brokerId ];
            prevbrokerDetails.push({
              [ brokerId ]: {
                "P&L": "0.00",
                maxPL: Number(brokerData.maxPL).toFixed(2),
                minPL:
                  brokerData.minPL === "Infinity"
                    ? Infinity
                    : Number(brokerData.minPL).toFixed(2),
                maxPLTime: brokerData.maxPLTime,
                minPLTime: brokerData.minPLTime,
              },
            });
          }
          portfolios.push({ ...port, brokerDetails: prevbrokerDetails });
        } else {
          portfolios.push({ ...port, brokerDetails: [] });
        }
      }

      dispatch(
        setPortfolios({
          portfolios: portfolios,
        })
      );
    });

    setExportModal(false);
  };

  const handleCheckboxChange1 = (item, isChecked) => {
    setSelectedRows((prevSelected) => {
      if (isChecked) {
        return [ ...prevSelected, item ];
      } else {
        return prevSelected.filter((selected) => selected !== item);
      }
    });
  };

  const mainUser = cookies.get("USERNAME");
  const dispatch = useDispatch();

  const { brokers: rows } = useSelector((state) => state.brokerReducer);
  const { strategies } = useSelector((state) => state.strategyReducer);
  const { orders } = useSelector((state) => state.orderBookReducer);
  const { positions } = useSelector((state) => state.positionReducer);
  const { holdings } = useSelector((state) => state.holdingReducer);

  const handleMsg = (Msg) => {
    const messageWithColor = { ...Msg, color: Msg.color || "black" };

    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;

      const lastMsg = previousConsoleMsgs[ 0 ];
      if (
        lastMsg &&
        lastMsg.msg === messageWithColor.msg &&
        lastMsg.user === messageWithColor.user &&
        lastMsg.strategy === messageWithColor.strategy &&
        lastMsg.portfolio === messageWithColor.portfolio
      ) {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ messageWithColor, ...previousConsoleMsgs.slice(1) ],
          })
        );
      } else {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ messageWithColor, ...previousConsoleMsgs ],
          })
        );
      }
    });
  };

  const [ isOpen1, setIsOpen1 ] = useState(false);
  const [ colopen1, setColopen1 ] = useState(false);
  const [ positionOq, setPositionOq ] = useState({ x: 0, y: 0 });

  const handleDragoq = (e, ui) => {
    const { x, y } = ui;
    setPositionOq({ x, y });
  };

  const resetPositionoq = () => {
    handleDragoq(null, { x: 0, y: 0 });
  };
  const toggleOpen1 = () => {
    setColopen1(!colopen1);

    const genderDetails = document.querySelector(".OP-details");
    const udbtton = document.querySelector(".UD-button");
    const sl = document.querySelector(".SL1");

    if (colopen1) {
      genderDetails.style.display = "none";
      sl.style.display = "none";
      udbtton.style.marginTop = "15px";
    } else {
      genderDetails.style.display = "block";
      sl.style.display = "block";
      udbtton.style.marginTop = "0";
    }
  };

  const handleOpenqtp = () => {
    setIsOpen1(true);
    setColopen1(false);
  };

  const handleClose1 = () => {
    setIsOpen1(false);
  };

  const [ isOpen, setIsOpen ] = useState(false);
  const [ isExitOpen, setisExitOpen ] = useState(false);
  const ExitcloseModal = () => {
    setisExitOpen(false);
  };
  const handleopenexit = () => {
    setisExitOpen(true);
  };

  const [ colopen, setColopen ] = useState(true);

  const toggleOpen = () => {
    setColopen(!colopen);

    const genderDetails = document.querySelector(".gender-details");
    const userButtons = document.querySelector(".user-details-button");
    const selectImage = document.querySelector(".select-image");
    const selectImages = document.querySelector(".select-images");
    const Check = document.querySelector(".check");
    const Sl = document.querySelector(".sl");

    if (colopen) {
      Check.style.display = "block";
      Sl.style.display = "block";
      genderDetails.style.display = "block";
      userButtons.style.marginTop = "0";
      selectImage.style.top = "8%";
      selectImages.style.top = "26.7%";
    } else {
      Check.style.display = "none";
      Sl.style.display = "none";
      genderDetails.style.display = "none";
      userButtons.style.marginTop = "15px";
      selectImage.style.top = "18.8%";
      selectImages.style.top = "58.8%";
    }
  };
  const handleOpen = () => {
    setColopen(true);
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
  };
  const [ position, setPosition ] = useState({ x: 0, y: 0 });

  const handleDrag = (e, ui) => {
    const { x, y } = ui;
    setPosition({ x, y });
  };

  const resetPosition = () => {
    handleDrag(null, { x: 0, y: 0 });
  };

  const RefreshButton = () => {
    fetchPortfolios();
  };

  const { executedPortfolios } = useSelector(
    (state) => state.executedPortfolioReducer
  );

  const { portfolios: portfolioDetails } = useSelector(
    (state) => state.portfolioReducer
  );

  const fetchExecutedPortfolios = async () => {
    try {
      if (executedPortfolios.length === 0) {
        handleMsg({
          msg: "No executed portfolios found to square off.",
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
        });
      } else {
        const executedPortfolioNames = executedPortfolios.map(
          (execPort) => execPort.portfolio_name
        );

        portfolioDetails.forEach((portfolio) => {
          if (executedPortfolioNames.includes(portfolio.portfolio_name)) {
            const linkedStrategy = strategies.filter(
              (strategy) => strategy.StrategyLabel === portfolio.strategy
            )[ 0 ];
            handleManualSquareOff(
              portfolio.portfolio_name,
              linkedStrategy.StrategyLabel,
              linkedStrategy.TradingAccount
            );
          }
        });
      }
    } catch (error) { }
  };
  const handleOpenqtp1 = () => {
    setShowModal(true);
    setMessage("Do you really want to Square Off all the Portfolio's?");
  };

  const { brokers } = useSelector((state) => state.brokerReducer);

  const handleManualSquareOff = async (
    portfolio,
    strategyLabel,
    TradingAccount
  ) => {
    const currentTime = new Date();
    const currentHours = currentTime.getHours();
    const currentMinutes = currentTime.getMinutes();

    if (
      !(
        (currentHours === 9 && currentMinutes >= 15) ||
        (currentHours > 9 && currentHours < 15) ||
        (currentHours === 15 && currentMinutes <= 30)
      )
    ) {
      handleMsg({
        msg: `Order not placed as current time is outside the allowed time window.`,
        logType: "INFO",
        timestamp: `${new Date().toLocaleString()}`,
        color: "red",
      });
      return;
    }
    try {
      const mappedUserIds = TradingAccount.split(", ");

      for (let index = 0; index < mappedUserIds.length; index++) {
        const rowData = brokers.filter(
          (row) => row.userId === mappedUserIds[ index ]
        )[ 0 ];

        if (rowData.inputDisabled) {
          const response = await fetchWithAuth(
            `/api/square_off_portfolio_level/${mainUser}/${portfolio}/${rowData.broker}/${rowData.userId}`,
            {
              method: "POST",
            }
          );
          if (!response.ok) {
            const errorData = await response.json();

            handleMsg({
              msg: errorData.message,
              logType: "ERROR",
              timestamp: `${new Date().toLocaleString()}`,
              user: rowData.userId,
              strategy: strategyLabel,
              portfolio: portfolio,
            });
          } else {
            const responseData = await response.json();

            handleMsg({
              msg: responseData.message,
              logType: "TRADING",
              timestamp: `${new Date().toLocaleString()}`,
              user: rowData.userId,
              strategy: strategyLabel,
              portfolio: portfolio,
            });
          }
        } else {
          handleMsg({
            msg: `Please login ${rowData.userId}, to perform square off.`,
            logType: "WARNING",
            timestamp: `${new Date().toLocaleString()}`,
            user: rowData.userId,
            strategy: strategyLabel,
            portfolio: portfolio,
          });
        }
      }
    } catch (error) { }
  };

  const [ currentDateTime, setCurrentDateTime ] = useState("");
  const { pathname } = useLocation();
  const navigate = useNavigate();
  useEffect(() => {
    const dateOptions = {
      day: "numeric",
      month: "short",
      year: "numeric",
      weekday: "short",
      hour: "numeric",
      minute: "numeric",
      timeZoneName: "short",
      hour12: false,
    };

    const updateDateTime = () => {
      const now = new Date();
      const formattedDateTime = now.toLocaleString("en-IN", dateOptions);
      const [ weekday, day, month, year ] = formattedDateTime.split(", ");
      setCurrentDateTime(`${day}, ${month} (${weekday}) ${year}`);
    };

    updateDateTime();
    const intervalId = setInterval(updateDateTime, 1000);

    return () => clearInterval(intervalId);
  }, []);

  const [ showBrokerLoginModal, setShowBrokerLoginModal ] = useState(false);
  const [ brokerLoginStatus, setBrokerLoginStatus ] = useState({});
  const [ isLoggingIn, setIsLoggingIn ] = useState(false);

  const handleBrokerLogin = async () => {
    setShowBrokerLoginModal(true);
    setBrokerLoginStatus({});
  };

  const closeBrokerLoginModal = () => {
    setShowBrokerLoginModal(false);
    setBrokerLoginStatus({});
    setIsLoggingIn(false);
  };

  const retryBrokerLogin = async (brokerIndex) => {
    const rowData = rows[ brokerIndex ];

    // Update status to validating
    setBrokerLoginStatus(prev => ({
      ...prev,
      [ brokerIndex ]: { status: 'validating', message: 'Validating credentials...' }
    }));

    // Validate mandatory fields based on broker type
    const unfilledFields = validateBrokerFields(rowData);
    if (unfilledFields.length > 0) {
      const errorMsg = `Please enter ${unfilledFields.join(", ")} to confirm broker login.`;
      setBrokerLoginStatus(prev => ({
        ...prev,
        [ brokerIndex ]: { status: 'error', message: errorMsg }
      }));
      handleErrorMessage(errorMsg, rowData.userId || "USER");
      return;
    }

    // Update status to connecting
    setBrokerLoginStatus(prev => ({
      ...prev,
      [ brokerIndex ]: { status: 'connecting', message: 'Connecting to broker...' }
    }));

    const requestData = prepareBrokerData(rowData, mainUser);

    try {
      const response = await fetchWithAuth("/api/datavalidation", {
        method: "POST",
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errorMsg = errorData.message || "An error occurred";
        setBrokerLoginStatus(prev => ({
          ...prev,
          [ brokerIndex ]: { status: 'error', message: errorMsg }
        }));
        handleErrorMessage(errorMsg, rowData.userId);
        return;
      }

      const responseData = await response.json();
      setBrokerLoginStatus(prev => ({
        ...prev,
        [ brokerIndex ]: { status: 'success', message: 'Login successful!' }
      }));

      // Update the broker data in Redux
      const updatedBrokers = [ ...rows ];
      updatedBrokers[ brokerIndex ] = {
        ...updatedBrokers[ brokerIndex ],
        ...mapResponseToRow(responseData, rowData.broker),
        status: "success",
      };
      dispatch(setBrokers({ brokers: updatedBrokers }));

      handleSuccessMessage(
        `Logged In successfully. - ${rowData.userId}`,
        rowData.userId || "USER"
      );
    } catch (error) {
      const errorMsg = `An unexpected error occurred: ${error.message}`;
      setBrokerLoginStatus(prev => ({
        ...prev,
        [ brokerIndex ]: { status: 'error', message: errorMsg }
      }));
      handleErrorMessage(errorMsg, rowData.userId);
    }
  };

  const performBrokerLogin = async () => {
    let allDisabled = true;
    let pseudoEnabled = false;

    const brokerUpdates = [ ...rows ];
    const statusUpdates = {};

    // Initialize status for all enabled brokers
    rows.forEach((data, index) => {
      if (data.enabled && !data.inputDisabled) {
        statusUpdates[ index ] = { status: 'pending', message: 'Preparing login...' };
      }
      if (data.enabled && data.broker !== "pseudo_account") {
        allDisabled = false;
      }
      if (data.enabled && data.broker === "pseudo_account") {
        pseudoEnabled = true;
      }
    });

    setBrokerLoginStatus(statusUpdates);

    if (allDisabled) {
      handleErrorMessage(
        "Please enable at least one broker (excluding pseudo accounts)"
      );
      return;
    }

    if (pseudoEnabled && allDisabled) {
      handleErrorMessage(
        "Please enable at least one non-pseudo broker along with the pseudo account"
      );
      return;
    }

    setIsLoggingIn(true);

    const loginPromises = rows.map(async (rowData, index) => {
      if (rowData.enabled && !rowData.inputDisabled) {
        // Update status to validating
        setBrokerLoginStatus(prev => ({
          ...prev,
          [ index ]: { status: 'validating', message: 'Validating credentials...' }
        }));

        // Validate mandatory fields based on broker type
        const unfilledFields = validateBrokerFields(rowData);
        if (unfilledFields.length > 0) {
          const errorMsg = `Please enter ${unfilledFields.join(
            ", "
          )} to confirm broker login.`;
          setBrokerLoginStatus(prev => ({
            ...prev,
            [ index ]: { status: 'error', message: errorMsg }
          }));
          handleErrorMessage(errorMsg, rowData.userId || "USER");
          brokerUpdates[ index ] = { ...rowData, status: "error", errorMsg };
          return;
        }

        // Update status to connecting
        setBrokerLoginStatus(prev => ({
          ...prev,
          [ index ]: { status: 'connecting', message: 'Connecting to broker...' }
        }));

        const requestData = prepareBrokerData(rowData, mainUser);

        try {
          const response = await fetchWithAuth("/api/datavalidation", {
            method: "POST",
            body: JSON.stringify(requestData),
          });
          if (!response.ok) {
            const errorData = await response.json();
            const errorMsg = errorData.message || "An error occurred";
            setBrokerLoginStatus(prev => ({
              ...prev,
              [ index ]: { status: 'error', message: errorMsg }
            }));
            brokerUpdates[ index ] = {
              ...rowData,
              status: "error",
              errorMsg: errorMsg,
            };
            handleErrorMessage(errorMsg, rowData.userId);
            return;
          }

          const responseData = await response.json();
          setBrokerLoginStatus(prev => ({
            ...prev,
            [ index ]: { status: 'success', message: 'Login successful!' }
          }));
          brokerUpdates[ index ] = {
            ...rowData,
            ...mapResponseToRow(responseData, rowData.broker),
            status: "success",
          };
          handleSuccessMessage(
            `Logged In successfully. - ${rowData.userId}`,
            rowData.userId || "USER"
          );
        } catch (error) {
          const errorMsg = `An unexpected error occurred: ${error.message}`;
          setBrokerLoginStatus(prev => ({
            ...prev,
            [ index ]: { status: 'error', message: errorMsg }
          }));
          brokerUpdates[ index ] = {
            ...rowData,
            status: "error",
            errorMsg: error.message,
          };
          handleErrorMessage(errorMsg, rowData.userId);
        }
      }
    });

    await Promise.all(loginPromises);
    dispatch(setBrokers({ brokers: brokerUpdates }));
    setIsLoggingIn(false);
  };

  /**
   * Validates broker fields based on broker type
   * Display Name is now mandatory for ALL brokers including pseudo accounts
   * @param {Object} rowData - The broker data to validate
   * @returns {Array} - Array of missing field names
   */
  const validateBrokerFields = (rowData) => {
    const missingFields = [];

    // Display Name is mandatory for ALL brokers including pseudo accounts
    if (!rowData.name || rowData.name.trim() === "") {
      missingFields.push("Display Name");
    }

    // User ID is mandatory for ALL brokers including pseudo accounts
    if (!rowData.userId || rowData.userId.trim() === "") {
      missingFields.push("User ID");
    }

    // Broker type is mandatory for ALL brokers
    if (!rowData.broker || rowData.broker.trim() === "") {
      missingFields.push("Broker");
    }

    // For non-pseudo accounts, additional fields are required
    if (rowData.broker !== "pseudo_account") {
      if (!rowData.qrCode || rowData.qrCode.trim() === "") {
        missingFields.push("QR Code/TOTP");
      }
      if (!rowData.password || rowData.password.trim() === "") {
        missingFields.push("Password");
      }
    }

    return missingFields;
  };

  // Keep the old function for backward compatibility if needed elsewhere
  const validateMandatoryFields = (rowData, mandatoryFields) => {
    return mandatoryFields.filter((field) => !rowData[ field ]);
  };

  const prepareBrokerData = (rowData, mainUser) => {
    const baseData = {
      userId: rowData.userId,
      apiKey: rowData.apiKey,
      qrCode: rowData.qrCode,
      password: rowData.password,
      broker: rowData.broker,
      imei: rowData.imei,
      display_name: rowData.name,
      max_loss: rowData.maxLoss,
      max_profit: rowData.maxProfit,
      mainUser,
    };

    if (rowData.broker === "fyers") {
      return {
        ...baseData,
        secretKey: rowData.secretKey,
        client_id: rowData.fyersclientId,
        apiKey: null,
      };
    }
    if (rowData.broker === "flattrade") {
      return {
        ...baseData,
        secretKey: rowData.secretKey,
        imei: null,
        fyersclientId: null,
      };
    }
    if (rowData.broker === "pseudo_account") {
      return { ...baseData, apiKey: null, pin: null, qrCode: null };
    }
    return baseData;
  };

  const mapResponseToRow = (responseData, broker) => {
    if (broker != "flattrade") {
      return {
        apiUserDetails: responseData.data.data.name,
        net: responseData.data.data.Net ? +responseData.data.data.Net : 0,
        inputDisabled: true,
      };
    } else if (broker === "flattrade") {
      return {
        apiUserDetails: responseData.data.prfname,
        net: responseData.data.Net ? +responseData.data.Net : 0,
        inputDisabled: true,
      };
    }
  };

  const handleErrorMessage = (msg, user = "USER") => {
    handleMsg({
      msg,
      logType: "WARNING",
      timestamp: `${new Date().toLocaleString()}`,
      user,
      color: "red",
    });
  };

  const handleSuccessMessage = (msg, user = "USER") => {
    handleMsg({
      msg,
      logType: "MESSAGE",
      timestamp: `${new Date().toLocaleString()}`,
      user,
    });
  };

  const downloadTableData = () => {
    let tableData = [];
    let fileName = "";
    if (pathname.includes("Positions")) {
      if (
        positions[ 0 ][ "Symbol" ] !== "" &&
        positions[ 0 ][ "Symbol" ] !== undefined
      ) {
        tableData = positions;
        fileName = "positions";
      } else {
        handleMsg({
          msg: "No Data Found in Positions to export",
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
        });
        return;
      }
    } else if (pathname.includes("Holdings")) {
      if (holdings[ 0 ][ "Symbol" ] !== "" && holdings[ 0 ][ "Symbol" ] !== undefined) {
        tableData = holdings;
        fileName = "holdings";
      } else {
        handleMsg({
          msg: "No Data Found in Holdings to export",
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
        });
        return;
      }
    } else if (pathname.includes("OrderFlow")) {
      if (
        orders[ 0 ][ "Stock Symbol" ] !== "" &&
        orders[ 0 ][ "Stock Symbol" ] !== undefined
      ) {
        tableData = orderBook;
        fileName = "orderBook";
      } else {
        handleMsg({
          msg: "No Data Found in Order Flow to export",
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
        });
        return;
      }
    } else if (pathname.includes("UserProfiles")) {
      if (rows.length === 1 && rows[ 0 ][ "broker" ] === "pseudo_account") {
        handleMsg({
          msg: "No Data Found in User Profiles to export",
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
        });
        return;
      } else {
        tableData = rows;
        fileName = "userProfiles";
      }
    } else if (pathname.includes("Strategies")) {
      if (
        strategies.length === 0 ||
        strategies[ 0 ][ "StrategyLabel" ] === "" ||
        strategies[ 0 ][ "StrategyLabel" ] === undefined
      ) {
        handleMsg({
          msg: "No Data Found in Strategies to export",
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
        });
        return;
      } else {
        tableData = strategies.map((obj) => {
          return {
            ...obj,
            TradingAccount: (obj.TradingAccount || "")
              .toString()
              .replace(/,/g, ";"),
            Multiplier: (obj.Multiplier || "").toString().replace(/,/g, ";"),
          };
        });
        fileName = "strategies";
      }
    } else if (pathname.includes("Portfolio")) {
      if (selectedRows.length === 0) {
        handleMsg({
          msg: "No rows selected for Portfolio export",
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
        });
        return;
      }

      const hasValidPortfolio = selectedRows.some(
        (obj) => obj.portfolio_id && obj.portfolio_name
      );
      if (!hasValidPortfolio) {
        handleMsg({
          msg: "No valid Portfolio data found for export",
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
        });
        return;
      }

      tableData = selectedRows.map((obj) => ({
        portfolio_id: obj.portfolio_id,
        portfolio_name: obj.portfolio_name,
        exchange: obj.exchange,
        stock_symbol: obj.stock_symbol,
        order_type: obj.order_type,
        variety: obj.variety,
        strategy: obj.strategy,
        Strategy_accounts_id: (obj.Strategy_accounts_id || "")
          .toString()
          .replace(/,/g, ";"),
        strategy_accounts: (obj.strategy_accounts || "")
          .toString()
          .replace(/,/g, ";"),
        product_type: obj.product_type,
        square_off_time: obj.square_off_time,
        start_time: obj.start_time,
        end_time: obj.end_time,
        enabled: obj.enabled,
        remarks: obj.remarks,
      }));
      fileName = `${mainUser}_portfolios`;
    } else {
      handleMsg({
        msg: "Invalid pathname for export",
        logType: "MESSAGE",
        timestamp: `${new Date().toLocaleString()}`,
      });
      return;
    }

    const exportToExcel = () => {
      const csvData = convertTableToCSV(tableData);
      const blob = new Blob([ csvData ], { type: "text/csv" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${fileName}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };

    const convertTableToCSV = (data) => {
      let csv = "";
      let legs = "";

      if (data.length > 0) {
        csv += Object.keys(data[ 0 ]).join(",") + "\n";
        data.forEach((row) => {
          csv +=
            Object.values(row)
              .map((value) => `"${value}"`)
              .join(",") + "\n";
        });
      } else {
        csv = "No data available\n";
      }

      if (pathname.includes("Portfolio")) {
        legs = selectedRows.flatMap((portfolio) => {
          return portfolio.legs.map((leg) => ({
            id: leg.id,
            portfolio_name: portfolio.portfolio_name,
            transaction_type: leg.transaction_type,
            option_type: leg.option_type,
            lots: leg.lots,
            quantity: leg.quantity,
            expiry_date: leg.expiry_date,
            strike: leg.strike,
            limit_price: leg.limit_price || null,
            sl_value: leg.sl_value || "0",
            stop_loss: leg.stop_loss || "None",
            target: leg.target || "None",
            tgt_value: leg.tgt_value || "0",
            trail_sl: leg.trail_sl || [ "", "" ],
            trail_tgt: leg.trail_tgt || [ "", "", "", "" ],
            showPopupSL1: leg.showPopupSL1 || false,
            showPopupSL: leg.showPopupSL || false,
          }));
        });

        if (legs.length > 0) {
          let legcsv = "";
          legcsv += Object.keys(legs[ 0 ]).join(",") + "\n";
          let prevPortfolioName = legs[ 0 ][ "portfolio_name" ];
          legs.forEach((row) => {
            if (prevPortfolioName !== row.portfolio_name) {
              legcsv += "\n";
              prevPortfolioName = row.portfolio_name;
            }
            legcsv +=
              Object.values(row)
                .map((value) => `"${value}"`)
                .join(",") + "\n";
          });

          csv += " \n LEGS \n" + legcsv;
        }
      }

      return csv;
    };
    closeModal();

    exportToExcel();
  };
  const fileInputRef = useRef(null);
  const fileInputStrategiesRef = useRef(null);

  const [ legsCount, setLegsCount ] = useState({});

  const handleFileUpload = (event) => {
    const file = event.target.files[ 0 ];
    if (file) {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: (result) => {
          const data = result.data;

          const keyMapping = {
            portfolio_id: "id",
            portfolio_name: "portfolio_name",
            exchange: "transaction_type",
            stock_symbol: "option_type",
            order_type: "lots",
            variety: "quantity",
            strategy: "expiry_date",
            Strategy_accounts_id: "strike",
            strategy_accounts: "limit_price",
            product_type: "sl_value",
            square_off_time: "stop_loss",
            start_time: "target",
            end_time: "tgt_value",
            enabled: "trail_sl",
            remarks: "trail_tgt",
            showPopupSL1: "showPopupSL1",
            showPopupSL: "showPopupSL",
            ltp: "ltp",
          };

          let portfoliosArray = [];
          let isLegsSection = false;

          data.forEach((entry) => {
            if (entry.portfolio_id.trim() === "LEGS") {
              isLegsSection = true;
              return;
            }

            if (!isLegsSection) {
              if (
                entry.portfolio_id.trim() !== "" &&
                entry.portfolio_id.trim() !== "id"
              ) {
                portfoliosArray.push({
                  portfolio_id: entry.portfolio_id,
                  portfolio_name: entry.portfolio_name,
                  exchange: entry.exchange,
                  stock_symbol: entry.stock_symbol,
                  order_type: entry.order_type,
                  variety: entry.variety,
                  strategy: entry.strategy,
                  Strategy_accounts_id: entry.Strategy_accounts_id,
                  strategy_accounts: entry.strategy_accounts,
                  product_type: entry.product_type,
                  square_off_time: entry.square_off_time,
                  start_time: entry.start_time,
                  end_time: entry.end_time,
                  enabled: entry.enabled,
                  remarks: entry.remarks,
                  legs: [],
                });
              }
            } else {
              if (
                entry.portfolio_id.trim() !== "" &&
                entry.portfolio_id.trim() !== "id"
              ) {
                let leg = {};
                for (let key in keyMapping) {
                  if (entry[ key ] !== undefined) {
                    leg[ keyMapping[ key ] ] = entry[ key ];
                  }
                }

                let portfolio = portfoliosArray.find(
                  (p) => p.portfolio_name === entry.portfolio_name
                );
                if (portfolio) {
                  portfolio.legs.push(leg);
                }
              }
            }
          });
          const legsCount = {};
          portfoliosArray.forEach((portfolio) => {
            legsCount[ portfolio.portfolio_id ] = portfolio.legs.length;
          });
          setLegsCount(legsCount);

          setExportModal(true);
          setPortfolio(portfoliosArray);
        },
        error: (error) => { },
      });
    }
  };
  const handleFileUploadStrategies = (event) => {
    const file = event.target.files[ 0 ];
    if (file) {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: (result) => {
          const data = result.data;

          const keyMapping = {
            StrategyLabel: "strategy_label",
            TradingAccount: "trading_account",
            MaxProfit: "max_profit",
            MaxLoss: "max_loss",
            Alias: "alias",
            Multiplier: "multiplier",
            Broker: "broker",
          };

          let strategiesArray = data.map((entry) => {
            let strategy = {};
            for (let key in keyMapping) {
              if (entry[ key ] !== undefined) {
                if ([ "TradingAccount", "Multiplier", "Broker" ].includes(key)) {
                  strategy[ keyMapping[ key ] ] = entry[ key ]
                    .split(";")
                    .map((item) => item.trim());
                } else {
                  strategy[ keyMapping[ key ] ] = entry[ key ];
                }
              }
            }
            return strategy;
          });

          handleMsg({
            msg: "Strategies imported successfully",
            logType: "MESSAGE",
            timestamp: `${new Date().toLocaleString()}`,
            strategy: "succesfully",
          });
        },
        error: (error) => { },
      });
    }
  };

  const handleButtonClick = () => {
    if (pathname.includes("Portfolio")) {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    } else {
      handleMsg({
        msg: "File import is allowed only for Portfolio",
        logType: "MESSAGE",
        timestamp: `${new Date().toLocaleString()}`,
      });
    }
  };

  const portfolioData = async (selectedRows) => {
    try {
      if (!selectedRows || selectedRows.length === 0) {
        throw new Error("Selected portfolios data is missing or empty");
      }

      for (let i = 0; i < selectedRows.length; i++) {
        const portfolio = selectedRows[ i ];

        const postData = {
          strategy: portfolio.strategy,
          exchange: portfolio.exchange,
          stock_symbol: portfolio.stock_symbol,
          portfolio_name: portfolio.portfolio_name,
          product_type: portfolio.product_type,
          order_type: portfolio.order_type,
          legs: portfolio.legs.map((leg) => ({
            id: leg.id,
            expiry_date: leg.expiry_date,
            limit_price: leg.limit_price || "",
            lots: leg.lots,
            option_type: leg.option_type,
            quantity: leg.quantity,
            sl_value: leg.sl_value || "",
            stop_loss: leg.stop_loss || "",
            strike: leg.strike,
            target: leg.target || "",
            tgt_value: leg.tgt_value || "",
            trail_sl: leg.trail_sl || "",
            trail_tgt: leg.trail_tgt || "",
            transaction_type: leg.transaction_type,
            showPopupSL1: leg.showPopupSL1 || false,
            showPopupSL: leg.showPopupSL || false,
            ltp: leg.ltp || "",
          })),
          start_time: portfolio.start_time || "00:00:00",
          end_time: portfolio.end_time || "00:00:00",
          square_off_time: portfolio.square_off_time || "00:00:00",
        };

        const response = await fetchWithAuth(`/api/store_portfolio/${mainUser}`, {
          method: "POST",
          body: JSON.stringify(postData),
        });
        const responseData = await response.json();
        fetchPortfolios();
        if (!response.ok) {
          throw new Error(
            responseData.message || "Something bad happened. Please try again"
          );
        }
      }
    } catch (error) {
      console.error(
        "Error sending data:",
        error.message || "Something bad happened. Please try again"
      );
    }
  };

  const handleManualSquareOffForAllRows = async () => {
    const currentTime = new Date();
    const currentHours = currentTime.getHours();
    const currentMinutes = currentTime.getMinutes();

    if (
      !(
        (currentHours === 9 && currentMinutes >= 15) ||
        (currentHours > 9 && currentHours < 15) ||
        (currentHours === 15 && currentMinutes <= 30)
      )
    ) {
      handleMsg({
        msg: `Order not placed as current time is outside the allowed time window.`,
        logType: "INFO",
        timestamp: `${new Date().toLocaleString()}`,
        color: "red",
      });
      return;
    }
    try {
      const mappedUserIds = rows.filter((row) => row.inputDisabled);
      if (mappedUserIds.length === 0) {
        handleMsg({
          msg: "Please log in at least one broker account",
          logType: "WARNING",
          timestamp: `${new Date().toLocaleString()}`,
        });
      }

      for (let index = 0; index < mappedUserIds.length; index++) {
        const rowData = mappedUserIds[ index ];
        if (rowData.inputDisabled) {
          let endpoint = "";
          let endpoint2 = "";
          if (rowData.broker === "angelone") {
            endpoint = `api/angelone_user_equity_sqoff/${mainUser}/${rowData.userId}`;
            endpoint2 = `api/angelone_user_options_sqoff/${mainUser}/${rowData.userId}`;
            await callEndpoint(endpoint, rowData);
            await callEndpoint(endpoint2, rowData);
          } else if (rowData.broker === "fyers") {
            endpoint = `api/fyers_user_equity_sqoff/${mainUser}/${rowData.userId}`;
            endpoint2 = `api/fyers_user_options_sqoff/${mainUser}/${rowData.userId}`;
            await callEndpoint(endpoint, rowData);
            await callEndpoint(endpoint2, rowData);
          } else if (rowData.broker === "flattrade") {
            endpoint = `api/flattrade_user_equity_sqoff/${mainUser}/${rowData.userId}`;
            endpoint2 = `api/flattrade_user_options_sqoff/${mainUser}/${rowData.userId}`;
            await callEndpoint(endpoint, rowData);
            await callEndpoint(endpoint2, rowData);
          } else if (rowData.broker === "pseudo_account") {
            endpoint = `/api/pseudo_user_options_sqoff/${mainUser}/${rowData.userId}`;
            endpoint2 = `api/pseudo_user_equity_sqoff/${mainUser}/${rowData.userId}`;
            await callEndpoint(endpoint, rowData);
            await callEndpoint(endpoint2, rowData);
          }
        }
      }
      setShowModal(false);
    } catch (error) { }
  };
  const callEndpoint = async (endpoint, rowData, endpointType) => {
    try {
      const response = await fetchWithAuth(endpoint, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        const message = errorData.message;
        handleMsg({
          msg: message,
          logType: "ERROR",
          timestamp: `${new Date().toLocaleString()}`,
          user: rowData.userId,
        });
      } else {
        const responseData = await response.json();
        const message = responseData.message;
        handleMsg({
          msg: message,
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
          user: rowData.userId,
        });
      }
    } catch (error) {
      console.error(
        `Error occurred while calling ${endpointType} API:`,
        error.message
      );
    }
  };

  const { placeOrderStart } = useSelector(
    (state) => state.placeOrderStartReducer
  );

  const enableAllPortfolioClick = async () => {
    try {
      const response = await fetchWithAuth(`/api/enable_all_portfolios/${mainUser}`, {
        method: "POST",

      });

      if (response.ok) {
        const msg = await response.json();
        handleMsg({
          msg: msg[ "message" ],
          logType: "MESSAGE",
          timestamp: ` ${new Date().toLocaleString()}`,
        });
        fetchPortfolios();
      }
    } catch (err) { }
  };
  const deleteAllPortfolioClick = async () => {
    try {
      const response = await fetchWithAuth(`/api/delete_all_portfolios/${mainUser}`, {
        method: "POST",

      });

      if (response.ok) {
        const msg = await response.json();
        handleMsg({
          msg: msg[ "message" ],

          logType: "MESSAGE",
          timestamp: ` ${new Date().toLocaleString()}`,
        });
        fetchPortfolios();
      }
    } catch (err) { }
  };
  const deleteEnabledPortfolioClick = async () => {
    try {
      const response = await fetchWithAuth(
        `/api/delete_all_enabled_portfolios/${mainUser}`,
        {
          method: "POST",

        }
      );

      if (response.ok) {
        const msg = await response.json();
        handleMsg({
          msg: msg[ "message" ],
          logType: "MESSAGE",
          timestamp: ` ${new Date().toLocaleString()}`,
        });
        fetchPortfolios();
      }
    } catch (err) { }
  };
  React.useEffect(() => {
    if (portfolioDetails.length === selectedRows.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [ selectedRows, portfolioDetails ]);
  const [ showModal, setShowModal ] = useState(false);
  const [ message, setMessage ] = useState("");
  return (
    <div className="right-sidebar">
      <div className="date-container">
        <span style={{ color: "#4661bd" }}>{currentDateTime}</span>
      </div>

      <div
        className="right-customize-one"
        onClick={async () => {
          handleBrokerLogin();
        }}
      >
        <span>Confirm broker login</span>
        <IoIosPeople style={{ fontSize: "30px", color: "green" }} />
      </div>
      <RightCustomizeTwo handleMsg={handleMsg} rows={rows} />
      <div className="right-customize-three">
        <ul>
          <li>
            <BsFillCaretLeftFill style={{ fontSize: "20px" }} />
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                gap: "10px",
              }}
            >
              <span>Option Trading</span>
              <TrendingUp size={24} color="#22c55e" />
            </div>

            {/* *************** */}
            <div className="three-options option-trading-dropdown">
              <div onClick={handleOpenqtp}>
                <span>Options Quick Trade Panel</span>
                <BarChart3 size={20} color="#3b82f6" />
              </div>
              <div>
                <span>Options Strategies payoff</span>
                <Target size={20} color="#f59e0b" />
              </div>
              <div>
                <span>Options Portfolio Execution</span>
                <Play size={20} color="#10b981" />
              </div>
              <div
                onClick={() => {
                  navigate("/Option_Chain");
                }}
              >
                <span>Options Chain</span>
                <Link size={20} color="#8b5cf6" />
              </div>
            </div>
          </li>
          <li>
            <BsFillCaretLeftFill style={{ fontSize: "20px" }} />
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                gap: "10px",
              }}
            >
              <span>Trading Tools</span>
              <Wrench size={24} color="#6b7280" />
            </div>

            <div className="three-options">
              <div onClick={handleOpen}>
                <span>Quick Trade Panel</span>
                <Zap size={20} color="#eab308" />
              </div>
              <div>
                <span>Show Quick Positions Window</span>
                <Eye size={20} color="#3b82f6" />
              </div>
              <div
                onClick={() => {
                  setMessage(
                    "Do you really want to Perform SquareOff for All Logged-In Users?"
                  );
                  setShowModal(true);
                }}
              >
                <span>Square Off All Logged-in Users</span>
                <Square size={20} color="#ef4444" />
              </div>
              <div>
                <span>Manual Refresh Script Masters</span>
                <RefreshCw size={20} color="#10b981" />
              </div>
              <div>
                <span>Chartink Screener Settings</span>
                <Settings size={20} color="#6b7280" />
              </div>
              <div>
                <span>MTM Analyser</span>
                <BarChart size={20} color="#8b5cf6" />
              </div>
              <div>
                <span>Schedule Start</span>
                <Calendar size={20} color="#f59e0b" />
              </div>
            </div>
          </li>
          <li>
            <BsFillCaretLeftFill style={{ fontSize: "20px" }} />
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                gap: "10px",
              }}
            >
              <span>Settings</span>
              <Settings size={24} color="#6b7280" />
            </div>

            <div className="three-options">
              <div>
                <span>Settings and Plugins Installation</span>
                <Download size={20} color="#3b82f6" />
              </div>
              <div>
                <span>Submit issue / Feature request</span>
                <MessageSquare size={20} color="#10b981" />
              </div>
              <div>
                <span>Save Filled Data in Grids</span>
                <Save size={20} color="#22c55e" />
              </div>
              <div>
                <span>TOTP Manager</span>
                <Shield size={20} color="#f59e0b" />
              </div>
              <div>
                <span>Change Password</span>
                <Key size={20} color="#ef4444" />
              </div>
              <div>
                <span>Reset App Configurations</span>
                <RotateCcw size={20} color="#f97316" />
              </div>
              <div>
                <span>Restore Auto Backups</span>
                <Upload size={20} color="#8b5cf6" />
              </div>
              <div>
                <span>Show Hidden Columns in Grids</span>
                <Columns size={20} color="#6b7280" />
              </div>
              <div>
                <span>Open Logs Folder</span>
                <FolderOpen size={20} color="#eab308" />
              </div>
              <div>
                <span>Last Update Change info</span>
                <Clock size={20} color="#06b6d4" />
              </div>
            </div>
          </li>
          {(pathname.includes("/Positions") ||
            pathname.includes("/Holding")) && (
              <>
                <li onClick={handleopenexit}>
                  <BsFillCaretLeftFill style={{ fontSize: "20px" }} />
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      gap: "10px",
                    }}
                  >
                    <span>Exit Position</span>
                    <IoExit
                      style={{
                        height: "30px",
                        width: "30px",
                      }}
                    />
                  </div>
                </li>
              </>
            )}
          {pathname.includes("/F&O") && (
            <>
              <li>
                <BsFillCaretLeftFill style={{ fontSize: "20px" }} />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <span>Add Portfolio</span>
                  <Plus size={24} color="#22c55e" />
                </div>

                {/* *************** */}
                <div
                  className="three-options"
                  style={{
                    marginBottom: "0px",
                    marginTop: "3.2rem",
                  }}
                  onClick={() => navigate("/F&O/AddPortfolio")}
                >
                  <div>
                    <span>Add Portfolio</span>
                    <AddIcon sx={{ color: "green", fontSize: 30 }} />
                  </div>
                </div>
              </li>
              <li>
                <BsFillCaretLeftFill style={{ fontSize: "20px" }} />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <span>Options</span>
                  <MoreHorizontal size={24} color="#6b7280" />
                </div>

                {/* *************** */}
                <div className="three-options" style={{ marginTop: "-14rem" }}>
                  <div
                    onClick={() => {
                      setMessage(
                        "Do you really want to enable All Portfolio's?"
                      );
                      setShowModal(true);
                    }}
                  >
                    <span>Enable All Portfolio(s)</span>
                    <CheckCircle size={20} color="#22c55e" />
                  </div>
                  <div
                    onClick={() => {
                      if (placeOrderStart) {
                        setMessage(
                          "Trading should be stopped to Delete All Portfolio's?"
                        );
                        setShowModal(true);
                      } else {
                        setMessage(
                          "Do you really want to Delete All Portfolio's?"
                        );
                        setShowModal(true);
                      }
                    }}
                  >
                    <span>Delete All Portfolio(s)</span>
                    <Trash2 size={20} color="#ef4444" />
                  </div>

                  <div
                    onClick={() => {
                      if (placeOrderStart) {
                        setMessage(
                          "Trading should be stopped to Delete All Enabled Portfolio's?"
                        );
                        setShowModal(true);
                      } else {
                        setMessage(
                          "Do you really want to Delete Enabled Portfolio's?"
                        );
                        setShowModal(true);
                      }
                    }}
                  >
                    <span>Delete Enabled Portfolio(s)</span>
                    <XCircle size={20} color="#f97316" />
                  </div>
                  <div>
                    <span>Delete Multiple Using Condition</span>
                    <Filter size={20} color="#8b5cf6" />
                  </div>
                  <div onClick={handleOpenqtp1}>
                    <span>SqOff All Portfolio(s)</span>
                    <Square size={20} color="#ef4444" />
                  </div>
                  <div>
                    <span>Export Grid</span>
                    <Download size={20} color="#3b82f6" />
                  </div>
                  <div>
                    <span>Reset Portiolio Form</span>
                    <RotateCcw size={20} color="#f59e0b" />
                  </div>
                  <div>
                    <span>Portfolio Column Settings</span>
                    <Columns size={20} color="#6b7280" />
                  </div>
                  <div>
                    <span>Portfolio Leg Col Settings</span>
                    <Columns size={20} color="#6b7280" />
                  </div>
                  <div>
                    <span>User Portfolio Col Settings</span>
                    <Columns size={20} color="#6b7280" />
                  </div>
                  <div>
                    <span>User Leg Column Settings</span>
                    <Columns size={20} color="#6b7280" />
                  </div>
                  <div>
                    <span>Clear MTM Analyser Data</span>
                    <Trash size={20} color="#ef4444" />
                  </div>
                </div>
              </li>
            </>
          )}
        </ul>
      </div>

      <div className="right-customize-four">
        <div onClick={RefreshButton}>
          <RefreshCw size={20} color="#10b981" />
          <span>Refresh</span>
        </div>
        <div>
          <input
            type="file"
            accept=".csv"
            ref={fileInputRef}
            data-x="true"
            style={{ display: "none" }}
            onChange={handleFileUpload}
          />
          {/* <input
            type="file"
            accept=".csv"
            ref={fileInputStrategiesRef}
            data-x="true"
            style={{ display: "none" }}
            onChange={handleFileUploadStrategies}
          /> */}
          <div onClick={handleButtonClick} style={{ cursor: "pointer" }}>
            <Upload size={20} color="#3b82f6" />
            <span>Import</span>
          </div>
        </div>
        {pathname.includes("Portfolio") ? (
          <div onClick={openModal}>
            <Download size={20} color="#f59e0b" />
            <span>Export</span>
          </div>
        ) : (
          <div onClick={downloadTableData}>
            <Download size={20} color="#f59e0b" />
            <span>Export</span>
          </div>
        )}
      </div>
      <OptionQuickTradePanel
        colopen1={colopen1}
        toggleOpen1={toggleOpen1}
        handleClose1={handleClose1}
        positionOq={positionOq}
        isOpen1={isOpen1}
        setIsOpen1={setIsOpen1}
        resetPositionoq={resetPositionoq}
        handleDragoq={handleDragoq}
        executedPortfolios={executedPortfolios}
      />
      <QuickTradePanel
        handleClose={handleClose}
        toggleOpen={toggleOpen}
        colopen={colopen}
        position={position}
        isOpen={isOpen}
        resetPosition={resetPosition}
        handleDrag={handleDrag}
      />
      <ExitPosition isExitOpen={isExitOpen} ExitcloseModal={ExitcloseModal} />
      <Modal
        isOpen={showModal}
        onRequestClose={() => setShowModal(false)}
        contentLabel="Error Modal"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            zIndex: 1000,
          },
          content: {
            width: "300px",
            height: "160px",
            margin: "auto",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            background: "white",
            borderRadius: "10px",
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
            padding: "20px 20px 10px",
          },
        }}
      >
        <p
          style={{
            textAlign: "center",
            fontSize: "18px",
            marginBottom: "-30px",
          }}
        >
          {message}
        </p>
        <div style={{ flex: 1 }}></div>
        <div
          className="modal-buttons"
          style={{
            marginBottom: "10px",
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          {message !== "Trading should be stopped to Delete All Portfolio's?" &&
            message !==
            "Trading should be stopped to Delete All Enabled Portfolio's?" && (
              <button
                style={{
                  backgroundColor: "#5cb85c",
                  color: "white",
                  border: "none",
                  cursor: "pointer",
                  padding: " 8px 10px",
                  borderRadius: "7px",
                  width: "75px",
                  marginRight: "30px",
                }}
                onClick={() => {
                  if (
                    message ===
                    "Do you really want to Perform SquareOff for All Logged-In Users?"
                  ) {
                    handleManualSquareOffForAllRows();
                  } else if (
                    message === "Do you really want to enable All Portfolio's?"
                  ) {
                    enableAllPortfolioClick();
                  } else if (
                    message === "Do you really want to Delete All Portfolio's?"
                  ) {
                    deleteAllPortfolioClick();
                  } else if (
                    message ===
                    "Do you really want to Delete Enabled Portfolio's?"
                  ) {
                    deleteEnabledPortfolioClick();
                  } else if (
                    message ===
                    "Do you really want to Square Off all the Portfolio's?"
                  ) {
                    fetchExecutedPortfolios();
                  }
                  setShowModal(false);
                }}
              >
                YES
              </button>
            )}
          {message !== "Trading should be stopped to Delete All Portfolio's?" &&
            message !==
            "Trading should be stopped to Delete All Enabled Portfolio's?" && (
              <button
                style={{
                  padding: "8px 16px",
                  borderRadius: "7px",
                  backgroundColor: "red",
                  color: "white",
                  border: "none",
                  cursor: "pointer",
                  width: "75px",
                  marginRight: "-10px",
                }}
                onClick={() => setShowModal(false)}
              >
                NO
              </button>
            )}

          {(message ===
            "Trading should be stopped to Delete All Portfolio's?" ||
            message ===
            "Trading should be stopped to Delete All Enabled Portfolio's?") && (
              <button
                style={{
                  padding: "8px 16px",
                  borderRadius: "7px",
                  backgroundColor: "red",
                  color: "white",
                  border: "none",
                  cursor: "pointer",
                  width: "75px",
                  marginRight: "-10px",
                }}
                onClick={() => setShowModal(false)}
              >
                Ok
              </button>
            )}
        </div>
      </Modal>
      <Modal
        isOpen={ExportModal}
        onRequestClose={closeModal}
        contentLabel="Confirm Delete Modal"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            zIndex: 1000,
          },
          content: {
            width: "70%",
            height: "fit-content",
            maxHeight: "400px",
            margin: "auto",
            display: "flex",
            flexDirection: "column",
            background: "white",
            borderRadius: "10px",
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
            padding: "20px",
            overflow: "auto",
          },
        }}
      >
        <div className="middle-main-container" style={{ width: "100%" }}>
          <div
            className="table-header"
            style={{
              padding: "5px",
              backgroundColor: "#4661bd",
              color: "white",
              borderTopLeftRadius: "5px",
              borderTopRightRadius: "5px",
            }}
          >
            <h2 style={{ margin: 0, textAlign: "center" }}>
              Manage Portfolio(s)
            </h2>
          </div>
          <div
            className="main-table"
            style={{
              maxHeight: "200px",
              overflowY: "auto",
              border: "1px solid #b3b0b0",
              borderBottom: "1px solid #b3b0b0",
              borderCollapse: "collapse",
              borderRadius: "5px",
            }}
          >
            <table className="table" style={{ width: "100%" }}>
              <thead style={{ position: "sticky", top: "0px", zIndex: 10 }}>
                <tr style={{ height: "35px" }}>
                  <th style={{ textAlign: "center", width: "70px" }}>
                    <span>Select All</span>
                    <input
                      type="checkbox"
                      checked={selectallExport}
                      onChange={handleselectallExport}
                      style={{ marginLeft: "15px" }}
                    />
                  </th>
                  <th style={{ textAlign: "center" }}>Status</th>
                  <th style={{ textAlign: "center" }}>Portfolio Name</th>
                  <th style={{ textAlign: "center" }}>legscount</th>
                  <th style={{ textAlign: "center" }}>Stock_symbol</th>
                  <th style={{ textAlign: "center" }}>Strategy</th>
                  <th style={{ textAlign: "center" }}>Starttime</th>
                  <th style={{ textAlign: "center" }}>Endtime</th>
                  <th style={{ textAlign: "center" }}>Sqoftime</th>
                </tr>
              </thead>
              <tbody
                className="tabletbody"
                style={{ backgroundColor: "#e8e6e6" }}
              >
                {portfolios?.map((item, index) => (
                  <tr key={index}>
                    <td style={{ textAlign: "center" }}>
                      <input
                        type="checkbox"
                        checked={selectedRows.some(
                          (row) => row.portfolio_id === item.portfolio_id
                        )}
                        onChange={(e) =>
                          handleCheckboxChange(item, e.target.checked)
                        }
                      />
                    </td>

                    <td>
                      <input
                        type="text"
                        style={{ padding: "6px" }}
                        disabled
                        value={
                          executedPortfolios.includes(item.portfolio_name)
                            ? "Completed"
                            : ""
                        }
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        value={item.portfolio_name}
                        style={{ padding: "6px" }}
                      />
                    </td>
                    <td>
                      <input
                        type="number"
                        value={legsCount[ item.portfolio_id ] || 0}
                        onInput={(e) => {
                          const value = e.target.value;
                          const sanitizedValue = value.replace(/[^0-9]/g, "");
                          e.target.value = sanitizedValue;
                        }}
                        style={{ padding: "6px" }}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        value={item.stock_symbol}
                        style={{ padding: "6px" }}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        value={item.strategy}
                        style={{ padding: "6px" }}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        defaultValue="00:00:00"
                        style={{
                          disable: "none",
                          padding: "6px",

                          textAlign: "center",
                        }}
                        value={item.start_time}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        defaultValue="00:00:00"
                        style={{
                          disable: "none",
                          padding: "6px",

                          textAlign: "center",
                        }}
                        value={item.end_time}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        defaultValue="00:00:00"
                        style={{
                          disable: "none",
                          padding: "6px",
                          textAlign: "center",
                        }}
                        value={item.square_off_time}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div
            style={{
              position: "relative",
              display: "flex",
              justifyContent: "space-around",
              zIndex: 1,
            }}
          >
            <button
              onClick={handleOkClick}
              style={{
                marginTop: "20px",
                width: "100px",
                color: "white",
                backgroundColor: "green",
                border: "none",
                borderRadius: "5px",
              }}
            >
              Import
            </button>
            <button
              onClick={handleCancelClick}
              style={{
                marginTop: "20px",
                width: "100px",
                color: "white",
                backgroundColor: "#d9534f",
                border: "none",
                borderRadius: "5px",
              }}
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>
      <Modal
        isOpen={ExportModal1}
        onRequestClose={closeModal}
        contentLabel="Confirm Delete Modal"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            zIndex: 1000,
          },
          content: {
            width: "70%",
            height: "fit-content",
            maxHeight: "400px",
            margin: "auto",
            display: "flex",
            flexDirection: "column",
            background: "white",
            borderRadius: "10px",
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
            padding: "20px",
            overflow: "auto",
          },
        }}
      >
        <div className="middle-main-container" style={{ width: "100%" }}>
          <div
            className="table-header"
            style={{
              padding: "5px",
              backgroundColor: "#4661bd",
              color: "white",
              borderTopLeftRadius: "5px",
              borderTopRightRadius: "5px",
            }}
          >
            <h2 style={{ margin: 0, textAlign: "center" }}>
              Manage Portfolio(s)
            </h2>
          </div>
          <div
            className="main-table"
            style={{
              maxHeight: "200px",
              overflowY: "auto",
              border: "1px solid #b3b0b0",
              borderBottom: "1px solid #b3b0b0",
              borderCollapse: "collapse",
              borderRadius: "5px",
            }}
          >
            <table className="table" style={{ width: "100%" }}>
              <thead style={{ position: "sticky", top: "0px", zIndex: 10 }}>
                <tr style={{ height: "35px" }}>
                  <th style={{ textAlign: "center", width: "100px" }}>
                    <span> Select</span>
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={handleSelectAllChange}
                      style={{ marginLeft: "15px" }}
                    />
                  </th>
                  <th style={{ textAlign: "center" }}>Status</th>
                  <th style={{ textAlign: "center" }}>Portfolio Name</th>
                  <th style={{ textAlign: "center" }}>legscount</th>
                  <th style={{ textAlign: "center" }}>Stock_symbol</th>
                  <th style={{ textAlign: "center" }}>Strategy</th>
                  <th style={{ textAlign: "center" }}>Starttime</th>
                  <th style={{ textAlign: "center" }}>Endtime</th>
                  <th style={{ textAlign: "center" }}>Sqoftime</th>
                </tr>
              </thead>
              <tbody
                className="tabletbody"
                style={{ backgroundColor: "#e8e6e6" }}
              >
                {portfolioDetails?.map((item, index) => (
                  <tr key={index}>
                    <td style={{ textAlign: "center" }}>
                      <input
                        type="checkbox"
                        checked={selectedRows.includes(item)}
                        onChange={(e) =>
                          handleCheckboxChange1(item, e.target.checked)
                        }
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        style={{ padding: "6px" }}
                        disabled
                        value={
                          executedPortfolios.includes(item.portfolio_name)
                            ? "Completed"
                            : ""
                        }
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        value={item.portfolio_name}
                        style={{ padding: "6px" }}
                      />
                    </td>
                    <td>
                      <input
                        type="number"
                        value={item.legs?.length || 0}
                        onInput={(e) => {
                          const value = e.target.value;
                          const sanitizedValue = value.replace(/[^0-9]/g, "");
                          e.target.value = sanitizedValue;
                        }}
                        style={{ padding: "6px" }}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        value={item.stock_symbol}
                        style={{ padding: "6px" }}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        value={item.strategy}
                        style={{ padding: "6px" }}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        defaultValue="00:00:00"
                        style={{
                          disable: "none",
                          padding: "6px",

                          textAlign: "center",
                        }}
                        value={item.start_time}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        defaultValue="00:00:00"
                        style={{
                          disable: "none",
                          padding: "6px",

                          textAlign: "center",
                        }}
                        value={item.end_time}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        defaultValue="00:00:00"
                        style={{
                          disable: "none",
                          padding: "6px",
                          textAlign: "center",
                        }}
                        value={item.square_off_time}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div
            style={{
              position: "relative",
              display: "flex",
              justifyContent: "flex-end",
              zIndex: 1,
              alignItems: "flex-end",
            }}
          >
            {pathname.includes("Portfolio") ? (
              <button
                onClick={downloadTableData}
                style={{
                  marginTop: "20px",
                  width: "150px",
                  color: "white",
                  backgroundColor: "green",
                  border: "none",
                  borderRadius: "5px",
                  cursor: "pointer",
                  height: "30px",
                  marginRight: "30px",
                }}
              >
                Export
              </button>
            ) : null}
            <button
              onClick={handleCancelClick}
              style={{
                marginTop: "20px",
                width: "100px",
                color: "white",
                backgroundColor: "#d9534f",
                border: "none",
                borderRadius: "5px",
              }}
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>

      {/* Enhanced Broker Login Modal */}
      <Modal
        isOpen={showBrokerLoginModal}
        onRequestClose={closeBrokerLoginModal}
        contentLabel="Broker Login Modal"
        className="broker-login-modal"
        overlayClassName="modal-overlay"
        ariaHideApp={false}
      >
        <div className="modal-header">
          <h3>Confirm Broker Login</h3>
          <button
            className="close-button"
            onClick={closeBrokerLoginModal}
          >
            ×
          </button>
        </div>

        <div className="modal-content">
          <div className="broker-validation-section">
            <h4>Broker Account Validation</h4>
            <p className="validation-info">
              <strong>Required fields for all brokers:</strong> Display Name, User ID, Broker Type<br />
              <strong>Additional fields for non-pseudo accounts:</strong> QR Code/TOTP, Password
            </p>
            <div className="broker-list">
              {rows.filter(row => row.enabled && !row.inputDisabled).map((broker, index) => {
                const actualIndex = rows.findIndex(r => r === broker);
                const status = brokerLoginStatus[ actualIndex ];
                const missingFields = validateBrokerFields(broker);

                return (
                  <div key={actualIndex} className="broker-item">
                    <div className="broker-info">
                      <div className="broker-header">
                        <span className="broker-name">{broker.name || broker.userId}</span>
                        <span className="broker-type">({broker.broker})</span>
                      </div>
                      <div className="broker-details">
                        <span>User ID: {broker.userId}</span>
                      </div>
                    </div>

                    <div className="broker-status">
                      {!status && missingFields.length > 0 && (
                        <div className="validation-error">
                          <span className="status-icon error">⚠</span>
                          <span>Missing: {missingFields.join(", ")}</span>
                        </div>
                      )}

                      {!status && missingFields.length === 0 && (
                        <div className="validation-success">
                          <span className="status-icon success">✓</span>
                          <span>Ready to login</span>
                        </div>
                      )}

                      {status && (
                        <div className={`login-status ${status.status}`}>
                          <span className={`status-icon ${status.status}`}>
                            {status.status === 'pending' && '⏳'}
                            {status.status === 'validating' && '🔍'}
                            {status.status === 'connecting' && '🔄'}
                            {status.status === 'success' && '✅'}
                            {status.status === 'error' && '❌'}
                          </span>
                          <span>{status.message}</span>
                          {status.status === 'error' && (
                            <button
                              className="retry-button"
                              onClick={() => retryBrokerLogin(actualIndex)}
                              disabled={isLoggingIn}
                            >
                              Retry
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Login Summary */}
          {Object.keys(brokerLoginStatus).length > 0 && (
            <div className="login-summary">
              <h4>Login Summary</h4>
              <div className="summary-stats">
                <div className="stat-item success">
                  <span className="stat-icon">✅</span>
                  <span>Successful: {Object.values(brokerLoginStatus).filter(s => s.status === 'success').length}</span>
                </div>
                <div className="stat-item error">
                  <span className="stat-icon">❌</span>
                  <span>Failed: {Object.values(brokerLoginStatus).filter(s => s.status === 'error').length}</span>
                </div>
                <div className="stat-item pending">
                  <span className="stat-icon">⏳</span>
                  <span>In Progress: {Object.values(brokerLoginStatus).filter(s => [ 'pending', 'validating', 'connecting' ].includes(s.status)).length}</span>
                </div>
              </div>
            </div>
          )}

          {rows.filter(row => row.enabled && !row.inputDisabled).length === 0 && (
            <div className="no-brokers-message">
              <p>No broker accounts are enabled or all accounts are already logged in.</p>
              <p>Please enable broker accounts in the User Profiles section.</p>
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button
            className="btn btn-primary"
            onClick={performBrokerLogin}
            disabled={isLoggingIn || rows.filter(row => row.enabled && !row.inputDisabled).length === 0}
          >
            {isLoggingIn ? 'Logging In...' : 'Start Login Process'}
          </button>
          <button
            className="btn btn-secondary"
            onClick={closeBrokerLoginModal}
            disabled={isLoggingIn}
          >
            Cancel
          </button>
        </div>
      </Modal>
    </div>
  );
}

export default RightNav;
